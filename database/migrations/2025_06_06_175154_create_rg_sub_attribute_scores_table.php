<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rg_sub_attribute_scores', function (Blueprint $table) {
            $table->id();
            $table->integer('organisation_location_id')->unsigned();
            $table->foreign('organisation_location_id')->references('id')->on('organisation_locations');

            $table->integer('rg_sub_attribute_id')->unsigned();
            $table->foreign('rg_sub_attribute_id')->references('id')->on('rg_sub_attributes');
            
            $table->integer('score')->unsigned();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rg_sub_attribute_scores');
    }
};
